@tailwind utilities;

/* Citation highlighting styles */
.citation-highlight {
  animation: highlightPulse 2s ease-in-out infinite;
  transition: all 0.3s ease;
}

.citation-highlight.scrolled-to {
  animation: highlightFlash 3s ease-in-out;
}

.citation-highlight.best-match {
  animation: bestMatchPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
  0%,
  100% {
    background-color: rgba(255, 255, 0, 0.8);
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
  }
  50% {
    background-color: rgba(255, 255, 0, 0.6);
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.7);
  }
}

@keyframes highlightFlash {
  0% {
    background-color: rgba(255, 255, 0, 0.8);
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
  }
  25% {
    background-color: rgba(255, 193, 7, 0.9);
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.8);
    transform: scale(1.02);
  }
  50% {
    background-color: rgba(255, 255, 0, 0.8);
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.6);
  }
  100% {
    background-color: rgba(255, 255, 0, 0.8);
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
    transform: scale(1);
  }
}

@keyframes bestMatchPulse {
  0%,
  100% {
    background-color: rgba(255, 215, 0, 0.9);
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.7);
    border-color: rgba(255, 165, 0, 0.9);
  }
  50% {
    background-color: rgba(255, 215, 0, 0.7);
    box-shadow: 0 0 15px rgba(255, 165, 0, 0.9);
    border-color: rgba(255, 140, 0, 1);
    transform: scale(1.01);
  }
}

/* PDF viewer improvements */
.react-pdf__Page__textContent {
  user-select: text;
}

.react-pdf__Page__textContent span {
  cursor: text;
}
