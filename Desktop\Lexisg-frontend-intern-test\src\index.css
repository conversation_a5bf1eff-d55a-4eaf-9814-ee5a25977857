@import "tailwindcss";

/* React-PDF styles */
.react-pdf__Page {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin: 1rem auto;
}

.react-pdf__Page__textContent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1;
}

.react-pdf__Page__textContent span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

/* Highlight styles for citation text */
.citation-highlight {
  background-color: rgba(255, 255, 0, 0.3);
  padding: 2px 4px;
  border-radius: 2px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    background-color: rgba(255, 255, 0, 0.3);
  }
  50% {
    background-color: rgba(255, 255, 0, 0.6);
  }
}
